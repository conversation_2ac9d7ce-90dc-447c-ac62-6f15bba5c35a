import React from 'react';
import { Mic, MicOff } from 'lucide-react';
import { Button } from './button';
import { cn } from '@/lib/utils';

interface VoiceInputProps {
  isListening: boolean;
  isSupported: boolean;
  onToggle: () => void;
  className?: string;
  size?: 'sm' | 'default' | 'lg' | 'icon' | 'icon-sm' | 'icon-lg';
  showWaveform?: boolean;
}

const VoiceInput: React.FC<VoiceInputProps> = ({
  isListening,
  isSupported,
  onToggle,
  className,
  size = 'default',
  showWaveform = true,
}) => {
  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Button
        onClick={onToggle}
        variant={isListening ? "voice" : "glass"}
        size={size}
        disabled={!isSupported}
        className={cn(
          "relative",
          isListening && "animate-pulse",
          !isSupported && "opacity-50 cursor-not-allowed"
        )}
      >
        {isListening ? (
          <MicOff className="h-4 w-4" />
        ) : (
          <Mic className="h-4 w-4" />
        )}
        {size !== 'icon' && size !== 'icon-sm' && size !== 'icon-lg' && (
          <span className="hidden sm:inline ml-1">
            {isListening ? 'Stop' : 'Voice'}
          </span>
        )}
        
        {/* Pulse effect for listening state */}
        {isListening && (
          <div className="absolute inset-0 rounded-lg bg-primary/20 animate-ping" />
        )}
      </Button>
      
      {/* Waveform Animation */}
      {showWaveform && isListening && (
        <div className="waveform">
          <div className="waveform-bar bg-primary"></div>
          <div className="waveform-bar bg-primary"></div>
          <div className="waveform-bar bg-primary"></div>
          <div className="waveform-bar bg-primary"></div>
          <div className="waveform-bar bg-primary"></div>
        </div>
      )}
    </div>
  );
};

export default VoiceInput;
