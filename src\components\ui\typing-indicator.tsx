import React from 'react';
import { Bot } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TypingIndicatorProps {
  className?: string;
  size?: 'sm' | 'default' | 'lg';
  showAvatar?: boolean;
}

const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  className,
  size = 'default',
  showAvatar = true,
}) => {
  const sizeClasses = {
    sm: 'gap-2',
    default: 'gap-3',
    lg: 'gap-4',
  };

  const avatarSizeClasses = {
    sm: 'h-6 w-6',
    default: 'h-8 w-8',
    lg: 'h-10 w-10',
  };

  const dotSizeClasses = {
    sm: 'w-1.5 h-1.5',
    default: 'w-2 h-2',
    lg: 'w-2.5 h-2.5',
  };

  return (
    <div className={cn("flex items-center", sizeClasses[size], className)}>
      {showAvatar && (
        <div className={cn(
          "rounded-full bg-earth-green flex items-center justify-center flex-shrink-0",
          avatarSizeClasses[size]
        )}>
          <Bot className={cn(
            "text-primary-foreground",
            size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4'
          )} />
        </div>
      )}
      
      <div className="chat-bubble glass-card p-3 rounded-2xl">
        <div className="typing-dots">
          <div className={cn("typing-dot bg-muted-foreground", dotSizeClasses[size])}></div>
          <div className={cn("typing-dot bg-muted-foreground", dotSizeClasses[size])}></div>
          <div className={cn("typing-dot bg-muted-foreground", dotSizeClasses[size])}></div>
        </div>
      </div>
    </div>
  );
};

export default TypingIndicator;
