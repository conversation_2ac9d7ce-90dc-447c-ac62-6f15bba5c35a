@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Agricultural Design System - Enhanced Nature-Inspired Theme */

@layer base {
  :root {
    /* Enhanced Nature Color Palette */
    --background: 120 20% 98%;
    --foreground: 120 15% 15%;

    --card: 120 25% 99%;
    --card-foreground: 120 15% 15%;

    --popover: 120 25% 99%;
    --popover-foreground: 120 15% 15%;

    /* Rich Green Primary Colors */
    --primary: 120 45% 35%;
    --primary-foreground: 120 20% 98%;
    --primary-glow: 120 55% 45%;
    --primary-soft: 120 35% 85%;

    /* Warm Earth Secondary Colors */
    --secondary: 35 40% 70%;
    --secondary-foreground: 120 15% 15%;

    /* Subtle Muted Colors */
    --muted: 120 15% 94%;
    --muted-foreground: 120 10% 45%;

    /* Accent Colors */
    --accent: 45 60% 65%;
    --accent-foreground: 120 15% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 120 20% 98%;

    /* Enhanced Border and Input Colors */
    --border: 120 20% 88%;
    --input: 120 15% 96%;
    --ring: 120 45% 35%;

    /* Hero Section Colors */
    --hero-bg: 120 35% 25%;
    --hero-fg: 120 20% 98%;

    /* Extended Nature Palette */
    --earth-brown: 25 45% 45%;
    --cane-green: 120 55% 40%;
    --field-green: 110 40% 30%;
    --harvest-gold: 45 75% 65%;
    --leaf-green: 120 60% 50%;
    --forest-green: 120 40% 25%;
    --sage-green: 120 25% 60%;

    /* Enhanced Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-earth: linear-gradient(135deg, hsl(var(--earth-brown)), hsl(var(--harvest-gold)));
    --gradient-field: linear-gradient(180deg, hsl(var(--field-green)), hsl(var(--cane-green)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--hero-bg)), hsl(var(--field-green)));
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1));
    --gradient-glass-dark: linear-gradient(135deg, rgba(0, 0, 0, 0.25), rgba(0, 0, 0, 0.1));

    /* Enhanced Shadows */
    --shadow-soft: 0 4px 20px hsl(var(--cane-green) / 0.15);
    --shadow-elevated: 0 8px 30px hsl(var(--primary) / 0.2);
    --shadow-glass: 0 8px 32px rgba(31, 38, 135, 0.37);
    --shadow-glow: 0 0 20px hsl(var(--primary-glow) / 0.3);

    /* Enhanced Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Enhanced Border Radius */
    --radius: 1rem;
    --radius-lg: 1.5rem;
    --radius-xl: 2rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark Mode Nature-Inspired Colors */
    --background: 120 15% 8%;
    --foreground: 120 20% 95%;

    --card: 120 15% 10%;
    --card-foreground: 120 20% 95%;

    --popover: 120 15% 10%;
    --popover-foreground: 120 20% 95%;

    /* Dark Mode Primary Colors */
    --primary: 120 55% 55%;
    --primary-foreground: 120 15% 8%;
    --primary-glow: 120 65% 65%;
    --primary-soft: 120 35% 25%;

    /* Dark Mode Secondary Colors */
    --secondary: 35 30% 25%;
    --secondary-foreground: 120 20% 95%;

    /* Dark Mode Muted Colors */
    --muted: 120 10% 15%;
    --muted-foreground: 120 15% 65%;

    /* Dark Mode Accent Colors */
    --accent: 45 50% 55%;
    --accent-foreground: 120 15% 8%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 120 20% 95%;

    /* Dark Mode Border and Input Colors */
    --border: 120 15% 20%;
    --input: 120 10% 12%;
    --ring: 120 55% 55%;

    /* Dark Mode Hero Colors */
    --hero-bg: 120 25% 15%;
    --hero-fg: 120 20% 95%;

    /* Dark Mode Nature Palette */
    --earth-brown: 25 35% 35%;
    --cane-green: 120 45% 35%;
    --field-green: 110 30% 25%;
    --harvest-gold: 45 65% 55%;
    --leaf-green: 120 50% 45%;
    --forest-green: 120 30% 20%;
    --sage-green: 120 20% 45%;

    /* Dark Mode Gradients */
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    --gradient-glass-dark: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2));

    /* Dark Mode Shadows */
    --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.3);
    --shadow-elevated: 0 8px 30px rgba(0, 0, 0, 0.4);
    --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.5);
    --shadow-glow: 0 0 20px hsl(var(--primary-glow) / 0.4);

    /* Sidebar Dark Mode */
    --sidebar-background: 120 15% 8%;
    --sidebar-foreground: 120 20% 95%;
    --sidebar-primary: 120 55% 55%;
    --sidebar-primary-foreground: 120 15% 8%;
    --sidebar-accent: 120 10% 15%;
    --sidebar-accent-foreground: 120 20% 95%;
    --sidebar-border: 120 15% 20%;
    --sidebar-ring: 120 55% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Enhanced Typography */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    letter-spacing: -0.025em;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Frosted Glass Effects */
  .glass {
    background: var(--gradient-glass);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: var(--gradient-glass-dark);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-glass);
  }

  .dark .glass-card {
    background: rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Enhanced Chat Bubble */
  .chat-bubble {
    position: relative;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    transition: var(--transition-smooth);
  }

  .chat-bubble:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-elevated);
  }

  /* Voice Input Animation */
  .voice-pulse {
    animation: voice-pulse 1.5s ease-in-out infinite;
  }

  @keyframes voice-pulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 hsl(var(--primary) / 0.7);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 0 10px hsl(var(--primary) / 0);
    }
  }

  /* Waveform Animation */
  .waveform {
    display: flex;
    align-items: center;
    gap: 2px;
  }

  .waveform-bar {
    width: 3px;
    background: currentColor;
    border-radius: 2px;
    animation: waveform 1.2s ease-in-out infinite;
  }

  .waveform-bar:nth-child(1) { animation-delay: 0s; height: 8px; }
  .waveform-bar:nth-child(2) { animation-delay: 0.1s; height: 12px; }
  .waveform-bar:nth-child(3) { animation-delay: 0.2s; height: 16px; }
  .waveform-bar:nth-child(4) { animation-delay: 0.3s; height: 12px; }
  .waveform-bar:nth-child(5) { animation-delay: 0.4s; height: 8px; }

  @keyframes waveform {
    0%, 100% { transform: scaleY(0.3); }
    50% { transform: scaleY(1); }
  }

  /* Typing Indicator */
  .typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
  }

  .typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: typing 1.4s ease-in-out infinite;
  }

  .typing-dot:nth-child(1) { animation-delay: 0s; }
  .typing-dot:nth-child(2) { animation-delay: 0.2s; }
  .typing-dot:nth-child(3) { animation-delay: 0.4s; }

  @keyframes typing {
    0%, 60%, 100% {
      transform: translateY(0);
      opacity: 0.4;
    }
    30% {
      transform: translateY(-10px);
      opacity: 1;
    }
  }

  /* Enhanced Button Hover Effects */
  .btn-glow:hover {
    box-shadow: var(--shadow-glow);
    transform: translateY(-2px);
  }

  /* Mobile-First Responsive Utilities */
  .mobile-fixed-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 50;
  }

  @media (min-width: 768px) {
    .mobile-fixed-bottom {
      position: relative;
      bottom: auto;
      left: auto;
      right: auto;
      z-index: auto;
    }
  }
}

.goog-te-gadget-icon {
  display: none !important;
}
